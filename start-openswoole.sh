#!/bin/bash

# MikhMon V3 OpenSwoole Startup Script

echo "Starting MikhMon V3 with OpenSwoole..."
echo ""

# Run system test first
echo "Running system compatibility test..."
php test-openswoole.php
if [ $? -ne 0 ]; then
    echo ""
    echo "System test failed. Please fix the issues above before starting the server."
    exit 1
fi

echo ""
echo "Running server start test..."
php test-server-start.php
if [ $? -ne 0 ]; then
    echo ""
    echo "Server start test failed. Please fix the issues above before starting the server."
    exit 1
fi

echo ""
echo "All tests passed! Starting server..."
echo ""

# Check if composer dependencies are installed
if [ ! -d "vendor" ]; then
    echo "Installing Composer dependencies..."
    composer install --no-dev --optimize-autoloader
fi

# Create necessary directories
mkdir -p logs sessions config
chmod 755 logs sessions

# Set environment if not already set
if [ -z "$MIKHMON_ENV" ]; then
    export MIKHMON_ENV=development
    echo "Environment set to: development"
fi

echo "Starting server on http://0.0.0.0:8080"
echo "Environment: $MIKHMON_ENV"
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
php server.php start
