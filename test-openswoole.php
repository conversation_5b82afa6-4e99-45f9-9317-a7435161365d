<?php
/**
 * OpenSwoole Test Script for MikhMon V3
 * 
 * This script tests the OpenSwoole setup and verifies all components are working
 */

echo "MikhMon V3 OpenSwoole Test Script\n";
echo "=================================\n\n";

$errors = [];
$warnings = [];

// Test 1: PHP Version
echo "1. Checking PHP version...\n";
$phpVersion = PHP_VERSION;
echo "   PHP Version: $phpVersion\n";
if (version_compare($phpVersion, '8.0.0', '<')) {
    $errors[] = "PHP 8.0 or higher is required. Current version: $phpVersion";
} else {
    echo "   ✓ PHP version is compatible\n";
}
echo "\n";

// Test 2: OpenSwoole Extension
echo "2. Checking OpenSwoole extension...\n";
if (extension_loaded('openswoole')) {
    $swooleVersion = phpversion('openswoole');
    echo "   ✓ OpenSwoole extension loaded (version: $swooleVersion)\n";
} else {
    $errors[] = "OpenSwoole extension is not loaded";
    echo "   ✗ OpenSwoole extension not found\n";
}
echo "\n";

// Test 3: Required PHP Extensions
echo "3. Checking required PHP extensions...\n";
$requiredExtensions = ['curl', 'gd', 'mbstring', 'json'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✓ $ext extension loaded\n";
    } else {
        $warnings[] = "$ext extension is not loaded (may cause issues)";
        echo "   ⚠ $ext extension not found\n";
    }
}
echo "\n";

// Test 4: Directory Permissions
echo "4. Checking directory permissions...\n";
$directories = ['logs', 'sessions', 'config'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "   ✓ Created directory: $dir\n";
    } else {
        echo "   ✓ Directory exists: $dir\n";
    }
    
    if (is_writable($dir)) {
        echo "   ✓ Directory writable: $dir\n";
    } else {
        $errors[] = "Directory not writable: $dir";
        echo "   ✗ Directory not writable: $dir\n";
    }
}
echo "\n";

// Test 5: Configuration Files
echo "5. Checking configuration files...\n";
$configFiles = ['config/development.php', 'config/production.php'];
foreach ($configFiles as $configFile) {
    if (file_exists($configFile)) {
        echo "   ✓ Configuration file exists: $configFile\n";
        
        // Test if configuration is valid PHP
        $config = @include $configFile;
        if (is_array($config)) {
            echo "   ✓ Configuration file is valid: $configFile\n";
        } else {
            $warnings[] = "Configuration file may have syntax errors: $configFile";
            echo "   ⚠ Configuration file may have issues: $configFile\n";
        }
    } else {
        $warnings[] = "Configuration file not found: $configFile";
        echo "   ⚠ Configuration file not found: $configFile\n";
    }
}
echo "\n";

// Test 6: Composer Dependencies
echo "6. Checking Composer dependencies...\n";
if (file_exists('vendor/autoload.php')) {
    echo "   ✓ Composer dependencies installed\n";
    require_once 'vendor/autoload.php';
    echo "   ✓ Autoloader working\n";
} else {
    $errors[] = "Composer dependencies not installed. Run: composer install";
    echo "   ✗ Composer dependencies not found\n";
}
echo "\n";

// Test 7: Core Classes
echo "7. Checking core classes...\n";
$coreClasses = [
    'src/OpenSwooleServer.php' => 'MikhMon\\OpenSwooleServer',
    'src/Router.php' => 'MikhMon\\Router',
    'src/SessionManager.php' => 'MikhMon\\SessionManager',
    'src/SessionCompatibility.php' => 'MikhMon\\SessionCompatibility',
];

foreach ($coreClasses as $file => $class) {
    if (file_exists($file)) {
        echo "   ✓ Core file exists: $file\n";
        
        if (class_exists($class)) {
            echo "   ✓ Class loadable: $class\n";
        } else {
            $warnings[] = "Class not loadable: $class";
            echo "   ⚠ Class not loadable: $class\n";
        }
    } else {
        $errors[] = "Core file missing: $file";
        echo "   ✗ Core file missing: $file\n";
    }
}
echo "\n";

// Test 8: Port Availability
echo "8. Checking port availability...\n";
$port = 8080;
$socket = @fsockopen('127.0.0.1', $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    $warnings[] = "Port $port is already in use";
    echo "   ⚠ Port $port is already in use\n";
} else {
    echo "   ✓ Port $port is available\n";
}
echo "\n";

// Test 9: Memory Limit
echo "9. Checking memory limit...\n";
$memoryLimit = ini_get('memory_limit');
echo "   Memory limit: $memoryLimit\n";
$memoryBytes = return_bytes($memoryLimit);
if ($memoryBytes < 128 * 1024 * 1024) { // 128MB
    $warnings[] = "Memory limit is low ($memoryLimit). Consider increasing to 256M or higher";
    echo "   ⚠ Memory limit is low\n";
} else {
    echo "   ✓ Memory limit is adequate\n";
}
echo "\n";

// Summary
echo "Test Summary\n";
echo "============\n";

if (empty($errors)) {
    echo "✓ All critical tests passed!\n";
} else {
    echo "✗ Critical errors found:\n";
    foreach ($errors as $error) {
        echo "  - $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠ Warnings:\n";
    foreach ($warnings as $warning) {
        echo "  - $warning\n";
    }
}

echo "\n";

if (empty($errors)) {
    echo "Your system is ready for MikhMon V3 with OpenSwoole!\n";
    echo "You can now start the server with: php server.php start\n";
} else {
    echo "Please fix the errors above before starting the server.\n";
    exit(1);
}

// Helper function to convert memory limit to bytes
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
