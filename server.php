<?php
/**
 * MikhMon V3 OpenSwoole Server
 *
 * This file starts the OpenSwoole HTTP server for MikhMon V3
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/OpenSwooleServer.php';

use Mikh<PERSON>on\OpenSwooleServer;

// Determine environment
$environment = $_SERVER['MIKHMON_ENV'] ?? 'development';

// Load configuration
$configFile = __DIR__ . "/config/{$environment}.php";
if (file_exists($configFile)) {
    $envConfig = require $configFile;
    $config = array_merge_recursive($defaultConfig ?? [], $envConfig);
} else {
    // Default configuration for development
    $config = [
        'host' => '0.0.0.0',
        'port' => 8080,
        'document_root' => __DIR__,
        'enable_static_handler' => true,
        'static_handler_locations' => ['/css', '/js', '/img', '/lang'],
        'worker_num' => 4,
        'task_worker_num' => 2,
        'max_request' => 1000,
        'dispatch_mode' => 2,
        'daemonize' => false,
        'log_file' => __DIR__ . '/logs/swoole.log',
        'log_level' => SWOOLE_LOG_INFO,
        'pid_file' => __DIR__ . '/logs/swoole.pid',
    ];
}

// Handle command line arguments
if (isset($argv[1])) {
    switch ($argv[1]) {
        case 'start':
            echo "Starting MikhMon OpenSwoole Server...\n";
            break;
        case 'stop':
            $pidFile = $config['pid_file'];
            if (file_exists($pidFile)) {
                $pid = (int)file_get_contents($pidFile);
                if ($pid > 0 && posix_kill($pid, 0)) {
                    posix_kill($pid, SIGTERM);
                    echo "Stopping server (PID: $pid)...\n";
                    exit(0);
                } else {
                    echo "Server is not running.\n";
                    exit(1);
                }
            } else {
                echo "PID file not found. Server may not be running.\n";
                exit(1);
            }
            break;
        case 'restart':
            // Stop first
            $pidFile = $config['pid_file'];
            if (file_exists($pidFile)) {
                $pid = (int)file_get_contents($pidFile);
                if ($pid > 0 && posix_kill($pid, 0)) {
                    posix_kill($pid, SIGTERM);
                    echo "Stopping server (PID: $pid)...\n";
                    sleep(2);
                }
            }
            echo "Starting MikhMon OpenSwoole Server...\n";
            break;
        case 'reload':
            $pidFile = $config['pid_file'];
            if (file_exists($pidFile)) {
                $pid = (int)file_get_contents($pidFile);
                if ($pid > 0 && posix_kill($pid, 0)) {
                    posix_kill($pid, SIGUSR1);
                    echo "Reloading server (PID: $pid)...\n";
                    exit(0);
                } else {
                    echo "Server is not running.\n";
                    exit(1);
                }
            } else {
                echo "PID file not found. Server may not be running.\n";
                exit(1);
            }
            break;
        case 'status':
            $pidFile = $config['pid_file'];
            if (file_exists($pidFile)) {
                $pid = (int)file_get_contents($pidFile);
                if ($pid > 0 && posix_kill($pid, 0)) {
                    echo "Server is running (PID: $pid)\n";
                    exit(0);
                } else {
                    echo "Server is not running (stale PID file)\n";
                    unlink($pidFile);
                    exit(1);
                }
            } else {
                echo "Server is not running\n";
                exit(1);
            }
            break;
        default:
            echo "Usage: php server.php {start|stop|restart|reload|status}\n";
            exit(1);
    }
}

// Create and start server
$server = new OpenSwooleServer($config);
$server->start();
