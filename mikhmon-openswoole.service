[Unit]
Description=MikhMon V3 OpenSwoole Server
After=network.target
Wants=network.target

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/var/www/mikhmon
ExecStart=/usr/bin/php /var/www/mikhmon/server.php
ExecReload=/bin/kill -USR1 $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mikhmon-openswoole
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=5

[Install]
WantedBy=multi-user.target
