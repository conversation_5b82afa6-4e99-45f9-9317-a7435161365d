<?php
/**
 * Session Test for OpenSwoole
 * 
 * This file tests session functionality under OpenSwoole
 */

// Simulate the session start that happens in admin.php
session_start();

// Test basic session functionality
if (!isset($_SESSION['test_counter'])) {
    $_SESSION['test_counter'] = 0;
}
$_SESSION['test_counter']++;

?>
<!DOCTYPE html>
<html>
<head>
    <title>OpenSwoole Session Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .info { color: blue; }
        .test-box { border: 1px solid #ccc; padding: 20px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>OpenSwoole Session Test</h1>
    
    <div class="test-box">
        <h2>Session Status</h2>
        <p class="info">Session ID: <?= session_id() ?></p>
        <p class="info">Session Status: <?= session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive' ?></p>
        <p class="success">Session Counter: <?= $_SESSION['test_counter'] ?></p>
    </div>
    
    <div class="test-box">
        <h2>Environment Info</h2>
        <p class="info">Running under OpenSwoole: <?= defined('RUNNING_UNDER_OPENSWOOLE') ? 'Yes' : 'No' ?></p>
        <p class="info">OpenSwoole Session Mode: <?= defined('OPENSWOOLE_SESSION_MODE') ? 'Yes' : 'No' ?></p>
        <p class="info">PHP Version: <?= PHP_VERSION ?></p>
        <p class="info">Server Software: <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></p>
    </div>
    
    <div class="test-box">
        <h2>Session Data</h2>
        <pre><?= print_r($_SESSION, true) ?></pre>
    </div>
    
    <div class="test-box">
        <h2>Actions</h2>
        <p><a href="?action=increment">Increment Counter</a></p>
        <p><a href="?action=reset">Reset Counter</a></p>
        <p><a href="?action=destroy">Destroy Session</a></p>
        <p><a href="test-session.php">Refresh Page</a></p>
    </div>
    
    <?php
    // Handle actions
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'increment':
                $_SESSION['test_counter']++;
                echo '<div class="test-box"><p class="success">Counter incremented!</p></div>';
                break;
            case 'reset':
                $_SESSION['test_counter'] = 0;
                echo '<div class="test-box"><p class="success">Counter reset!</p></div>';
                break;
            case 'destroy':
                session_destroy();
                echo '<div class="test-box"><p class="success">Session destroyed!</p></div>';
                break;
        }
    }
    ?>
    
    <div class="test-box">
        <h2>Test Results</h2>
        <?php if (session_status() === PHP_SESSION_ACTIVE): ?>
            <p class="success">✓ Session is working correctly</p>
        <?php else: ?>
            <p style="color: red;">✗ Session is not active</p>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['test_counter'])): ?>
            <p class="success">✓ Session data is persistent</p>
        <?php else: ?>
            <p style="color: red;">✗ Session data is not persistent</p>
        <?php endif; ?>
    </div>
    
</body>
</html>
