<?php
/**
 * Memory Limit Fix for OpenSwoole
 * 
 * This script fixes memory limit issues that can occur with OpenSwoole
 */

echo "Fixing Memory Limit Issues...\n";
echo "=============================\n\n";

// Get current memory limit
$currentLimit = ini_get('memory_limit');
echo "Current memory limit: $currentLimit\n";

// Convert to bytes for comparison
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

$currentBytes = return_bytes($currentLimit);
$recommendedBytes = 256 * 1024 * 1024; // 256MB

echo "Current memory limit in bytes: " . number_format($currentBytes) . "\n";
echo "Recommended minimum: " . number_format($recommendedBytes) . " (256MB)\n\n";

if ($currentBytes < $recommendedBytes) {
    echo "⚠️  Memory limit is too low for OpenSwoole\n";
    echo "Attempting to increase memory limit...\n";
    
    // Try to increase memory limit
    $newLimit = '256M';
    $result = ini_set('memory_limit', $newLimit);
    
    if ($result !== false) {
        echo "✅ Memory limit increased to: " . ini_get('memory_limit') . "\n";
    } else {
        echo "❌ Failed to increase memory limit programmatically\n";
        echo "\nManual fix required:\n";
        echo "1. Edit your php.ini file\n";
        echo "2. Set: memory_limit = 256M\n";
        echo "3. Restart your web server/PHP-FPM\n";
        echo "\nOr set environment variable:\n";
        echo "export PHP_MEMORY_LIMIT=256M\n";
    }
} else {
    echo "✅ Memory limit is adequate\n";
}

// Check current memory usage
$currentUsage = memory_get_usage(true);
$peakUsage = memory_get_peak_usage(true);

echo "\nCurrent memory usage: " . number_format($currentUsage) . " bytes (" . round($currentUsage / 1024 / 1024, 2) . " MB)\n";
echo "Peak memory usage: " . number_format($peakUsage) . " bytes (" . round($peakUsage / 1024 / 1024, 2) . " MB)\n";

// Test memory allocation
echo "\nTesting memory allocation...\n";
try {
    $testArray = array_fill(0, 100000, 'test');
    unset($testArray);
    echo "✅ Memory allocation test passed\n";
} catch (Exception $e) {
    echo "❌ Memory allocation test failed: " . $e->getMessage() . "\n";
}

echo "\n📋 Summary:\n";
echo "- Memory warnings during OpenSwoole startup are usually harmless\n";
echo "- They occur when OpenSwoole tries to optimize memory settings\n";
echo "- The server should still start and work correctly\n";
echo "- For production, ensure memory_limit is at least 256M\n";

echo "\n🚀 Memory check complete!\n";
