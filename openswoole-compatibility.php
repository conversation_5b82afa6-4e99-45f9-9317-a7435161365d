<?php
/**
 * OpenSwoole Compatibility Patches for MikhMon V3
 * 
 * This file contains patches that can be applied to make MikhMon more compatible
 * with OpenSwoole. Include this file at the beginning of admin.php and index.php
 */

// Only apply patches if running under OpenSwoole
if (!defined('RUNNING_UNDER_OPENSWOOLE')) {
    return;
}

/**
 * Patch 1: Handle ob_start with gzhandler
 */
if (!defined('OPENSWOOLE_OB_PATCHED')) {
    define('OPENSWOOLE_OB_PATCHED', true);

    // Create a replacement for ob_gzhandler that does nothing
    if (!function_exists('ob_gzhandler_replacement')) {
        function ob_gzhandler_replacement($buffer, $mode = null) {
            // In OpenSwoole, we don't need PHP-level gzip compression
            return $buffer;
        }
    }

    // Override the global ob_start calls in included files
    function patch_ob_start_calls() {
        // This will be handled by the Router's startOutputBuffering method
        return true;
    }
}

/**
 * Patch 2: Session handling
 */
if (!function_exists('session_start_patched')) {
    function session_start_patched($options = []) {
        // If session is already started in OpenSwoole mode, return true
        if (isset($GLOBALS['openswoole_session_started']) && $GLOBALS['openswoole_session_started']) {
            return true;
        }
        
        // Try to start session normally, but suppress warnings
        $result = @session_start($options);
        
        // If it fails due to headers already sent, mark as started anyway
        if (!$result && headers_sent()) {
            $GLOBALS['openswoole_session_started'] = true;
            return true;
        }
        
        return $result;
    }
}

/**
 * Patch 3: Header functions
 */
if (!function_exists('header_patched')) {
    function header_patched($header, $replace = true, $response_code = 0) {
        // Get OpenSwoole response object
        $response = $GLOBALS['swoole_response'] ?? null;
        
        if ($response && $response->isWritable()) {
            if ($response_code > 0) {
                $response->status($response_code);
            }
            
            // Parse header
            $parts = explode(':', $header, 2);
            if (count($parts) === 2) {
                $name = trim($parts[0]);
                $value = trim($parts[1]);
                $response->header($name, $value);
            }
        } else {
            // Fallback to normal header function
            @header($header, $replace, $response_code);
        }
    }
}

/**
 * Patch 4: Error reporting
 */
if (!function_exists('error_reporting_patched')) {
    function error_reporting_patched($level = null) {
        if ($level === null) {
            return error_reporting();
        }
        
        // In OpenSwoole mode, we might want to suppress certain errors
        if (defined('RUNNING_UNDER_OPENSWOOLE')) {
            // Suppress session and header warnings
            $level = $level & ~E_WARNING;
        }
        
        return error_reporting($level);
    }
}

/**
 * Apply patches by replacing function calls
 */
function apply_openswoole_patches() {
    // This function can be called to apply runtime patches
    // For now, we rely on the bootstrap file to handle most compatibility issues
}

/**
 * Helper function to check if we can safely use headers
 */
function can_send_headers() {
    if (defined('RUNNING_UNDER_OPENSWOOLE')) {
        $response = $GLOBALS['swoole_response'] ?? null;
        return $response && $response->isWritable();
    }
    return !headers_sent();
}

/**
 * Helper function to safely redirect
 */
function safe_redirect($url) {
    if (defined('RUNNING_UNDER_OPENSWOOLE')) {
        $response = $GLOBALS['swoole_response'] ?? null;
        if ($response && $response->isWritable()) {
            $response->status(302);
            $response->header('Location', $url);
            $response->end();
            return;
        }
    }
    
    // Fallback to normal redirect
    if (!headers_sent()) {
        header("Location: $url");
        exit();
    }
}

/**
 * Helper function to safely echo and exit
 */
function safe_echo_and_exit($content) {
    if (defined('RUNNING_UNDER_OPENSWOOLE')) {
        $response = $GLOBALS['swoole_response'] ?? null;
        if ($response && $response->isWritable()) {
            $response->end($content);
            return;
        }
    }
    
    echo $content;
    exit();
}

// Auto-apply some patches
if (defined('RUNNING_UNDER_OPENSWOOLE')) {
    // Set error handler to suppress session warnings
    set_error_handler(function($errno, $errstr, $errfile, $errline) {
        // Suppress session-related warnings
        if (strpos($errstr, 'session_start()') !== false || 
            strpos($errstr, 'Cannot modify header') !== false ||
            strpos($errstr, 'headers have already been sent') !== false) {
            return true;
        }
        return false;
    }, E_WARNING);
}
