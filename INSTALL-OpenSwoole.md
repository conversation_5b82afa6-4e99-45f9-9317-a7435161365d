# MikhMon V3 OpenSwoole Installation Guide

This guide will help you install and configure MikhMon V3 with OpenSwoole support.

## Prerequisites

- PHP 8.0 or higher
- Composer
- Git (for cloning the repository)
- Root or sudo access (for system-wide installation)

## Quick Start (Docker)

The fastest way to get started is using Docker:

```bash
# Clone the repository
git clone <repository-url>
cd mikhmonv3-dev

# Start with OpenSwoole
docker-compose --profile openswoole up -d

# Access the application
open http://localhost:8080
```

## Manual Installation

### Step 1: Install PHP and Extensions

#### Ubuntu/Debian:
```bash
# Install PHP 8.2
sudo apt update
sudo apt install php8.2-cli php8.2-dev php8.2-curl php8.2-gd php8.2-mbstring

# Install build tools
sudo apt install build-essential autoconf pkg-config libssl-dev
```

#### CentOS/RHEL:
```bash
# Install PHP 8.2
sudo dnf install php82-cli php82-devel php82-curl php82-gd php82-mbstring

# Install build tools
sudo dnf groupinstall "Development Tools"
sudo dnf install openssl-devel
```

### Step 2: Install OpenSwoole Extension

```bash
# Install via PECL
sudo pecl install openswoole

# Add to php.ini
echo "extension=openswoole" | sudo tee -a /etc/php/8.2/cli/php.ini

# Verify installation
php -m | grep openswoole
```

### Step 3: Install Composer

```bash
# Download and install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### Step 4: Setup Application

```bash
# Clone the repository
git clone <repository-url>
cd mikhmonv3-dev

# Install dependencies
composer install --no-dev --optimize-autoloader

# Create necessary directories with proper permissions
mkdir -p logs sessions config
chmod 755 logs sessions config

# Ensure log files can be created
touch logs/swoole.log logs/swoole.pid
chmod 644 logs/swoole.log logs/swoole.pid

# Configuration files should already exist
# No need to copy as they're included in the repository
```

### Step 5: Configure Application

Edit `config/production.php` for your environment:

```php
<?php
return [
    'server' => [
        'host' => '0.0.0.0',
        'port' => 8080,
        'worker_num' => 8,          // Adjust based on CPU cores
        'daemonize' => true,        // Set to true for production
        'log_file' => '/var/log/mikhmon/swoole.log',
        'pid_file' => '/var/run/mikhmon/swoole.pid',
    ],
    // ... other configurations
];
```

### Step 6: Start the Server

#### Development Mode:
```bash
# Start in foreground
php server.php start

# Or use the startup script
./start-openswoole.sh
```

#### Production Mode:
```bash
# Set environment
export MIKHMON_ENV=production

# Start server
php server.php start

# Or install as systemd service
sudo cp mikhmon-openswoole.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable mikhmon-openswoole
sudo systemctl start mikhmon-openswoole
```

## Configuration

### Environment Variables

- `MIKHMON_ENV`: Set to `production` or `development` (default: `development`)

### Server Management Commands

```bash
# Start server
php server.php start

# Stop server
php server.php stop

# Restart server
php server.php restart

# Reload workers (zero-downtime)
php server.php reload

# Check status
php server.php status
```

### Systemd Service Management

```bash
# Start service
sudo systemctl start mikhmon-openswoole

# Stop service
sudo systemctl stop mikhmon-openswoole

# Restart service
sudo systemctl restart mikhmon-openswoole

# Check status
sudo systemctl status mikhmon-openswoole

# View logs
sudo journalctl -u mikhmon-openswoole -f
```

## Performance Tuning

### System Limits

Edit `/etc/security/limits.conf`:
```
www-data soft nofile 65535
www-data hard nofile 65535
```

### Kernel Parameters

Edit `/etc/sysctl.conf`:
```
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 4096
```

Apply changes:
```bash
sudo sysctl -p
```

### PHP Configuration

Edit `php.ini`:
```ini
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
```

## Monitoring

### Health Check

```bash
# Check server health
curl http://localhost:8080/health
```

### Log Files

- **Server logs**: `logs/swoole.log`
- **System logs**: `journalctl -u mikhmon-openswoole`
- **Application logs**: Check MikhMon's existing log files

### Performance Monitoring

```bash
# Monitor server processes
ps aux | grep server.php

# Monitor memory usage
free -h

# Monitor network connections
netstat -tulpn | grep :8080
```

## Troubleshooting

### Common Issues

1. **OpenSwoole extension not found**
   ```bash
   # Check if extension is loaded
   php -m | grep openswoole
   
   # Reinstall if needed
   sudo pecl uninstall openswoole
   sudo pecl install openswoole
   ```

2. **Permission denied errors**
   ```bash
   # Fix permissions
   sudo chown -R www-data:www-data /var/www/mikhmon
   sudo chmod -R 755 /var/www/mikhmon
   ```

3. **Memory limit warnings:**
   ```
   PHP Warning: Failed to set memory limit to 1024 bytes
   ```

   **Solution:**
   ```bash
   # Run memory fix script
   php fix-memory-limit.php

   # Or manually set memory limit
   echo "memory_limit = 256M" >> /etc/php/php.ini
   ```

4. **Log directory errors:**
   ```
   open(/var/log/mikhmon/swoole.log) failed. Error: No such file or directory
   ```

   **Solution:**
   ```bash
   # Create log directories with proper permissions
   sudo mkdir -p /var/log/mikhmon /var/run/mikhmon
   sudo chown $USER:$USER /var/log/mikhmon /var/run/mikhmon

   # Or use local directories (recommended for development)
   mkdir -p logs sessions
   chmod 755 logs sessions
   ```

5. **Task worker errors:**
   ```
   Server::start_check(): onTask event callback must be set
   ```

   **Solution:**
   - This is fixed in the latest version
   - Task workers are now disabled by default
   - No action needed

6. **Port already in use**
   ```bash
   # Find process using port
   sudo lsof -i :8080

   # Kill process or change port in configuration
   ```

4. **Memory issues**
   ```bash
   # Increase PHP memory limit
   echo "memory_limit = 512M" | sudo tee -a /etc/php/8.2/cli/php.ini
   ```

### Debug Mode

Enable debug mode in development:

```bash
# Set environment
export MIKHMON_ENV=development

# Start with debug logging
php server.php start
```

## Security Considerations

### Firewall

```bash
# Allow HTTP traffic
sudo ufw allow 8080/tcp

# Or for specific IP ranges
sudo ufw allow from ***********/24 to any port 8080
```

### SSL/TLS

For production, use a reverse proxy (Nginx/Apache) for SSL termination:

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Migration from Traditional Setup

1. **Backup existing data**
2. **Install OpenSwoole** (follow steps above)
3. **Test in development** environment first
4. **Switch Docker profiles** or update server configuration
5. **Monitor performance** and adjust settings as needed

## Support

For issues:
1. Check logs in `logs/swoole.log`
2. Verify OpenSwoole installation: `php -m | grep openswoole`
3. Test health endpoint: `curl http://localhost:8080/health`
4. Review configuration files in `config/` directory

For MikhMon-specific issues, refer to the main documentation.
