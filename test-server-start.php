<?php
/**
 * Test Server Start for OpenSwoole
 * 
 * This script tests if the OpenSwoole server can start without errors
 */

echo "Testing OpenSwoole Server Start...\n";
echo "==================================\n\n";

// Check if OpenSwoole extension is loaded
if (!extension_loaded('openswoole')) {
    echo "❌ OpenSwoole extension not found\n";
    exit(1);
}

echo "✅ OpenSwoole extension found\n";

// Test configuration loading
$environment = 'development';
$configFile = __DIR__ . "/config/{$environment}.php";

$defaultConfig = [
    'host' => '0.0.0.0',
    'port' => 8080,
    'document_root' => __DIR__,
    'enable_static_handler' => true,
    'static_handler_locations' => ['/css', '/js', '/img', '/lang'],
    'worker_num' => 2, // Reduced for testing
    'task_worker_num' => 1,
    'max_request' => 100,
    'dispatch_mode' => 2,
    'daemonize' => false,
    'log_file' => __DIR__ . '/logs/swoole.log',
    'log_level' => SWOOLE_LOG_INFO,
    'pid_file' => __DIR__ . '/logs/swoole.pid',
];

if (file_exists($configFile)) {
    $envConfig = require $configFile;
    if (isset($envConfig['server'])) {
        $config = array_merge($defaultConfig, $envConfig['server']);
    } else {
        $config = array_merge($defaultConfig, $envConfig);
    }
    echo "✅ Configuration loaded from $configFile\n";
} else {
    $config = $defaultConfig;
    echo "✅ Using default configuration\n";
}

// Create necessary directories
$dirs = ['logs', 'sessions'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Created directory: $dir\n";
        } else {
            echo "❌ Failed to create directory: $dir\n";
            exit(1);
        }
    } else {
        echo "✅ Directory exists: $dir\n";
    }
}

// Test class loading
try {
    require_once 'vendor/autoload.php';
    require_once 'src/OpenSwooleServer.php';
    echo "✅ Classes loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Failed to load classes: " . $e->getMessage() . "\n";
    exit(1);
}

// Test server creation (without starting)
try {
    $server = new \MikhMon\OpenSwooleServer($config);
    echo "✅ Server object created successfully\n";
} catch (Exception $e) {
    echo "❌ Failed to create server: " . $e->getMessage() . "\n";
    exit(1);
}

// Test port availability
$socket = @fsockopen($config['host'], $config['port'], $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    echo "⚠️  Port {$config['port']} is already in use\n";
    echo "   You may need to stop other services or change the port\n";
} else {
    echo "✅ Port {$config['port']} is available\n";
}

echo "\n🎉 All tests passed! Server should start successfully.\n";
echo "Run: php server.php start\n";
