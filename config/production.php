<?php

/**
 * Production Configuration for MikhMon V3 OpenSwoole
 */

return [
    'server' => [
        'host' => '0.0.0.0',
        'port' => 8080,
        'worker_num' => 8,              // Increase for production
        'task_worker_num' => 0,         // Disabled for now (can be enabled if needed)
        'max_request' => 10000,         // Higher limit for production
        'dispatch_mode' => 2,
        'daemonize' => true,            // Run as daemon in production
        'log_file' => '/var/log/mikhmon/swoole.log',
        'log_level' => SWOOLE_LOG_WARNING,  // Less verbose in production
        'pid_file' => '/var/run/mikhmon/swoole.pid',
        'user' => 'www-data',
        'group' => 'www-data',
    ],
    
    'session' => [
        'lifetime' => 7200,             // 2 hours
        'path' => '/var/lib/mikhmon/sessions',
        'cleanup_interval' => 3600,     // 1 hour
    ],
    
    'security' => [
        'max_upload_size' => '10M',
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
        'rate_limit' => [
            'enabled' => true,
            'requests_per_minute' => 60,
        ],
    ],
    
    'performance' => [
        'enable_gzip' => true,
        'cache_static_files' => true,
        'static_file_max_age' => 86400, // 24 hours
    ],
    
    'monitoring' => [
        'health_check_enabled' => true,
        'metrics_enabled' => true,
        'log_slow_requests' => true,
        'slow_request_threshold' => 1.0, // seconds
    ],
];
