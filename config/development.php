<?php

/**
 * Development Configuration for MikhMon V3 OpenSwoole
 */

return [
    'server' => [
        'host' => '0.0.0.0',
        'port' => 8080,
        'worker_num' => 2,              // Lower for development
        'task_worker_num' => 1,         // Lower for development
        'max_request' => 100,           // Lower for development (easier debugging)
        'dispatch_mode' => 2,
        'daemonize' => false,           // Don't run as daemon in development
        'log_file' => __DIR__ . '/../logs/swoole.log',
        'log_level' => SWOOLE_LOG_DEBUG, // Verbose logging for development
        'pid_file' => __DIR__ . '/../logs/swoole.pid',
    ],
    
    'session' => [
        'lifetime' => 3600,             // 1 hour
        'path' => __DIR__ . '/../sessions',
        'cleanup_interval' => 1800,     // 30 minutes
    ],
    
    'security' => [
        'max_upload_size' => '50M',     // Higher limit for development
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'svg'],
        'rate_limit' => [
            'enabled' => false,         // Disabled for development
            'requests_per_minute' => 0,
        ],
    ],
    
    'performance' => [
        'enable_gzip' => false,         // Disabled for easier debugging
        'cache_static_files' => false,  // Disabled for development
        'static_file_max_age' => 0,     // No caching
    ],
    
    'monitoring' => [
        'health_check_enabled' => true,
        'metrics_enabled' => true,
        'log_slow_requests' => true,
        'slow_request_threshold' => 0.5, // Lower threshold for development
    ],
    
    'debug' => [
        'enabled' => true,
        'show_errors' => true,
        'error_reporting' => E_ALL,
    ],
];
