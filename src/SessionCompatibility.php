<?php

namespace MikhMon;

/**
 * Session Compatibility Layer for OpenSwoole
 * 
 * This class provides compatibility functions to make existing PHP session
 * functions work with OpenSwoole's stateless nature.
 */
class SessionCompatibility
{
    private static ?SessionManager $sessionManager = null;
    private static bool $sessionStarted = false;

    public static function setSessionManager(SessionManager $sessionManager): void
    {
        self::$sessionManager = $sessionManager;
    }

    public static function sessionStart(): bool
    {
        if (self::$sessionStarted) {
            return true;
        }

        if (!self::$sessionManager) {
            return false;
        }

        // Initialize $_SESSION with current session data
        $_SESSION = self::$sessionManager->getAll();
        self::$sessionStarted = true;

        return true;
    }

    public static function sessionDestroy(): bool
    {
        if (!self::$sessionManager) {
            return false;
        }

        self::$sessionManager->destroy();
        $_SESSION = [];
        self::$sessionStarted = false;

        return true;
    }

    public static function sessionId(?string $id = null): string
    {
        if (!self::$sessionManager) {
            return '';
        }

        if ($id !== null) {
            // Setting session ID is not supported in this implementation
            return '';
        }

        return self::$sessionManager->getCurrentSessionId() ?? '';
    }

    public static function sessionName(?string $name = null): string
    {
        // Session name is fixed in our implementation
        return 'MIKHMON_SESSID';
    }

    public static function sessionRegenerateId(bool $deleteOldSession = false): bool
    {
        // Session regeneration is handled automatically by SessionManager
        return true;
    }

    public static function sessionWriteClose(): bool
    {
        if (!self::$sessionManager || !self::$sessionStarted) {
            return false;
        }

        // Save current $_SESSION data to SessionManager
        if (isset($_SESSION) && is_array($_SESSION)) {
            foreach ($_SESSION as $key => $value) {
                self::$sessionManager->set($key, $value);
            }
        }

        return true;
    }

    public static function sessionStatus(): int
    {
        if (self::$sessionStarted) {
            return PHP_SESSION_ACTIVE;
        }

        return PHP_SESSION_NONE;
    }

    public static function sessionUnset(): bool
    {
        $_SESSION = [];
        return true;
    }

    public static function sessionCommit(): bool
    {
        return self::sessionWriteClose();
    }

    public static function sessionAbort(): bool
    {
        self::$sessionStarted = false;
        return true;
    }

    public static function sessionReset(): bool
    {
        if (!self::$sessionManager) {
            return false;
        }

        $_SESSION = self::$sessionManager->getAll();
        return true;
    }
}

// Override PHP session functions if they haven't been overridden already
if (!function_exists('session_start_original')) {
    // Store original functions
    if (function_exists('session_start')) {
        function session_start_original() {
            return \session_start();
        }
    }

    // Override session functions
    function session_start(): bool
    {
        return \MikhMon\SessionCompatibility::sessionStart();
    }

    function session_destroy(): bool
    {
        return \MikhMon\SessionCompatibility::sessionDestroy();
    }

    function session_id(?string $id = null): string
    {
        return \MikhMon\SessionCompatibility::sessionId($id);
    }

    function session_name(?string $name = null): string
    {
        return \MikhMon\SessionCompatibility::sessionName($name);
    }

    function session_regenerate_id(bool $deleteOldSession = false): bool
    {
        return \MikhMon\SessionCompatibility::sessionRegenerateId($deleteOldSession);
    }

    function session_write_close(): bool
    {
        return \MikhMon\SessionCompatibility::sessionWriteClose();
    }

    function session_status(): int
    {
        return \MikhMon\SessionCompatibility::sessionStatus();
    }

    function session_unset(): bool
    {
        return \MikhMon\SessionCompatibility::sessionUnset();
    }

    function session_commit(): bool
    {
        return \MikhMon\SessionCompatibility::sessionCommit();
    }

    function session_abort(): bool
    {
        return \MikhMon\SessionCompatibility::sessionAbort();
    }

    function session_reset(): bool
    {
        return \MikhMon\SessionCompatibility::sessionReset();
    }
}
