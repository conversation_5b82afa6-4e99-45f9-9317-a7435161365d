<?php

namespace MikhMon;

use OpenSwoole\HTTP\Request;
use OpenSwoole\HTTP\Response;

class Router
{
    private SessionManager $sessionManager;
    private string $documentRoot;

    public function __construct()
    {
        $this->documentRoot = dirname(__DIR__);
    }

    public function handle(Request $request, Response $response, SessionManager $sessionManager): void
    {
        $this->sessionManager = $sessionManager;

        // Load OpenSwoole bootstrap FIRST
        require_once __DIR__ . '/OpenSwooleBootstrap.php';

        // Load session compatibility layer
        require_once __DIR__ . '/SessionCompatibility.php';
        SessionCompatibility::setSessionManager($this->sessionManager);

        // Set up PHP environment for legacy code
        $this->setupPhpEnvironment($request, $response);

        $uri = $request->server['request_uri'];
        $path = parse_url($uri, PHP_URL_PATH);

        // Handle static files (already handled by OpenSwoole static handler)
        if ($this->isStaticFile($path)) {
            $this->serveStaticFile($path, $response);
            return;
        }

        // Route to appropriate PHP file
        $this->routeToPhp($request, $response, $path);
    }

    private function setupPhpEnvironment(Request $request, Response $response): void
    {
        // Set up $_SERVER superglobal
        $_SERVER = array_merge($_SERVER ?? [], $request->server ?? []);
        $_SERVER['REQUEST_METHOD'] = $request->getMethod();
        $_SERVER['REQUEST_URI'] = $request->server['request_uri'];
        $_SERVER['QUERY_STRING'] = $request->server['query_string'] ?? '';
        $_SERVER['HTTP_HOST'] = $request->header['host'] ?? 'localhost';
        $_SERVER['HTTPS'] = isset($request->header['https']) ? 'on' : 'off';
        $_SERVER['REMOTE_ADDR'] = $request->server['remote_addr'] ?? '127.0.0.1';
        $_SERVER['HTTP_USER_AGENT'] = $request->header['user-agent'] ?? '';

        // Set up $_GET superglobal
        $_GET = $request->get ?? [];

        // Set up $_POST superglobal
        $_POST = $request->post ?? [];

        // Set up $_COOKIE superglobal
        $_COOKIE = $request->cookie ?? [];

        // Set up $_FILES superglobal
        $_FILES = $request->files ?? [];

        // Store response object for later use
        $GLOBALS['swoole_response'] = $response;

        // Set up custom session handling BEFORE any output
        $this->setupSessionHandling();
    }

    private function setupSessionHandling(): void
    {
        // Set up $_SESSION superglobal
        $_SESSION = $this->sessionManager->getAll();

        // Start session through compatibility layer
        SessionCompatibility::sessionStart();

        // Register shutdown function to save session
        register_shutdown_function(function() {
            $this->saveSessionData();
        });
    }

    private function saveSessionData(): void
    {
        if (isset($_SESSION) && is_array($_SESSION)) {
            foreach ($_SESSION as $key => $value) {
                $this->sessionManager->set($key, $value);
            }
        }
    }

    private function isStaticFile(string $path): bool
    {
        $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        return in_array(strtolower($extension), $staticExtensions);
    }

    private function serveStaticFile(string $path, Response $response): void
    {
        $filePath = $this->documentRoot . $path;
        
        if (!file_exists($filePath) || !is_file($filePath)) {
            $response->status(404);
            $response->end('File not found');
            return;
        }
        
        $mimeType = $this->getMimeType($filePath);
        $response->header('Content-Type', $mimeType);
        $response->header('Cache-Control', 'public, max-age=3600');
        
        $response->sendfile($filePath);
    }

    private function getMimeType(string $filePath): string
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'ico' => 'image/x-icon',
            'svg' => 'image/svg+xml',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    private function routeToPhp(Request $request, Response $response, string $path): void
    {
        // Handle health check endpoint
        if ($path === '/health' || $path === '/health.php') {
            $this->handleHealthCheck($response);
            return;
        }

        // Determine which PHP file to include based on the path
        $phpFile = $this->determinePhpFile($path, $request);

        if (!$phpFile || !file_exists($phpFile)) {
            $response->status(404);
            $response->header('Content-Type', 'text/html; charset=utf-8');
            $response->end($this->get404Page());
            return;
        }

        // Include compatibility patches
        require_once __DIR__ . '/../openswoole-compatibility.php';

        // Start output buffering with multiple levels to catch all output
        ob_start();

        // Override header() function to prevent headers being sent
        $this->overrideHeaderFunctions($response);

        try {
            // Include the PHP file
            include $phpFile;

            $output = ob_get_contents();
            ob_end_clean();

            // Only send response if it hasn't been sent already
            if (!$response->isWritable()) {
                return;
            }

            // Send response
            $response->header('Content-Type', 'text/html; charset=utf-8');
            $response->end($output);

        } catch (\Throwable $e) {
            ob_end_clean();
            throw $e;
        }
    }

    private function overrideHeaderFunctions(Response $response): void
    {
        // Store response object globally for header functions
        $GLOBALS['swoole_response_for_headers'] = $response;

        // Override header function if not already done
        if (!function_exists('header_override_done')) {
            function header_override_done() { return true; }

            function header(string $header, bool $replace = true, int $responseCode = 0): void
            {
                $response = $GLOBALS['swoole_response_for_headers'] ?? null;
                if (!$response) return;

                if ($responseCode > 0) {
                    $response->status($responseCode);
                }

                // Parse header
                $parts = explode(':', $header, 2);
                if (count($parts) === 2) {
                    $name = trim($parts[0]);
                    $value = trim($parts[1]);
                    $response->header($name, $value);
                }
            }

            function http_response_code(int $responseCode = 0): int
            {
                $response = $GLOBALS['swoole_response_for_headers'] ?? null;
                if ($response && $responseCode > 0) {
                    $response->status($responseCode);
                }
                return $responseCode ?: 200;
            }
        }
    }

    private function handleHealthCheck(Response $response): void
    {
        $health = [
            'status' => 'ok',
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => 'OpenSwoole',
            'version' => '1.0.0',
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
        ];

        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($health, JSON_PRETTY_PRINT));
    }

    private function get404Page(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>404 - Page Not Found</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; display: inline-block; }
        .back-link { margin-top: 20px; }
        .back-link a { color: #007bff; text-decoration: none; }
        .back-link a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="error">
        <h1>404 - Page Not Found</h1>
        <p>The requested page could not be found.</p>
    </div>
    <div class="back-link">
        <a href="/admin.php?id=login">← Back to Login</a>
    </div>
</body>
</html>';
    }

    private function determinePhpFile(string $path, Request $request): ?string
    {
        // Handle root path
        if ($path === '/' || $path === '') {
            // Check if there's a session parameter
            if (isset($_GET['session'])) {
                return $this->documentRoot . '/index.php';
            }
            return $this->documentRoot . '/admin.php';
        }
        
        // Handle admin.php routes
        if (strpos($path, '/admin.php') === 0 || isset($_GET['id'])) {
            return $this->documentRoot . '/admin.php';
        }
        
        // Handle main application routes
        if (isset($_GET['session']) || isset($_GET['hotspot']) || isset($_GET['ppp']) || isset($_GET['report'])) {
            return $this->documentRoot . '/index.php';
        }
        
        // Handle direct PHP file access
        $phpFile = $this->documentRoot . $path;
        if (pathinfo($phpFile, PATHINFO_EXTENSION) === 'php' && file_exists($phpFile)) {
            return $phpFile;
        }
        
        // Default to admin.php for unknown routes
        return $this->documentRoot . '/admin.php';
    }
}
