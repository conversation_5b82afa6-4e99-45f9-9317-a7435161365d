<?php

/**
 * OpenSwoole Bootstrap for MikhMon V3
 * 
 * This file sets up the OpenSwoole environment and overrides PHP functions
 * that are incompatible with OpenSwoole's stateless nature.
 */

// Prevent multiple inclusions
if (defined('OPENSWOOLE_BOOTSTRAP_LOADED')) {
    return;
}
define('OPENSWOOLE_BOOTSTRAP_LOADED', true);

// Mark that we're running under OpenSwoole
define('RUNNING_UNDER_OPENSWOOLE', true);

/**
 * Override output buffering functions to work with OpenSwoole
 */
if (!function_exists('ob_gzhandler_override')) {
    
    function ob_gzhandler_override($buffer, $mode = null) {
        // In OpenSwoole, we don't need gzip compression at the PHP level
        // OpenSwoole can handle this more efficiently
        return $buffer;
    }
    
    // Store original ob_start if it exists
    if (function_exists('ob_start')) {
        function ob_start_original($callback = null, $chunk_size = 0, $flags = PHP_OUTPUT_HANDLER_STDFLAGS) {
            return \ob_start($callback, $chunk_size, $flags);
        }
    }
    
    // Override ob_start to handle gzhandler
    function ob_start($callback = null, $chunk_size = 0, $flags = PHP_OUTPUT_HANDLER_STDFLAGS): bool {
        // Replace ob_gzhandler with our override
        if ($callback === 'ob_gzhandler') {
            $callback = 'ob_gzhandler_override';
        }
        
        return ob_start_original($callback, $chunk_size, $flags);
    }
}

/**
 * Override exit() and die() to work properly with OpenSwoole
 */
if (!function_exists('exit_override')) {
    
    function exit_override($status = 0): void {
        // In OpenSwoole, we should not exit the entire process
        // Instead, we throw an exception that can be caught by the router
        throw new \Exception("Script exit called with status: $status", is_int($status) ? $status : 0);
    }
    
    function die_override($status = 0): void {
        exit_override($status);
    }
    
    // Note: We can't actually override exit() and die() as they are language constructs
    // But we can provide alternatives and modify the code to use them
}

/**
 * Override header-related functions
 */
if (!function_exists('headers_sent_override')) {
    
    function headers_sent_override(&$filename = null, &$linenum = null): bool {
        // In OpenSwoole, headers are not sent until response->end() is called
        return false;
    }
    
    function headers_list_override(): array {
        // Return empty array as we handle headers differently
        return [];
    }
    
    function header_remove_override(?string $name = null): void {
        // Handle header removal through OpenSwoole response object
        $response = $GLOBALS['swoole_response_for_headers'] ?? null;
        if ($response && $name) {
            // OpenSwoole doesn't have a direct way to remove headers
            // We could implement this by tracking headers separately
        }
    }
}

/**
 * Override session-related constants and functions
 */
if (!defined('PHP_SESSION_DISABLED')) {
    define('PHP_SESSION_DISABLED', 0);
    define('PHP_SESSION_NONE', 1);
    define('PHP_SESSION_ACTIVE', 2);
}

/**
 * Set up error handling for OpenSwoole
 */
function openswoole_error_handler($errno, $errstr, $errfile, $errline) {
    // Don't handle session-related warnings in OpenSwoole mode
    if (strpos($errstr, 'session_start()') !== false && 
        strpos($errstr, 'headers have already been sent') !== false) {
        return true; // Suppress this error
    }
    
    // Don't handle header-related warnings
    if (strpos($errstr, 'Cannot modify header information') !== false) {
        return true; // Suppress this error
    }
    
    // Let other errors be handled normally
    return false;
}

// Set the custom error handler
set_error_handler('openswoole_error_handler', E_WARNING | E_NOTICE);

/**
 * Override some problematic functions that might cause issues
 */
if (!function_exists('fastcgi_finish_request_override')) {
    function fastcgi_finish_request_override(): bool {
        // This function doesn't apply to OpenSwoole
        return true;
    }
}

/**
 * Set up OpenSwoole-specific globals
 */
$GLOBALS['openswoole_mode'] = true;
$GLOBALS['openswoole_headers_sent'] = false;

/**
 * Helper function to check if we're running under OpenSwoole
 */
function is_openswoole_mode(): bool {
    return defined('RUNNING_UNDER_OPENSWOOLE') && RUNNING_UNDER_OPENSWOOLE === true;
}

/**
 * Helper function to get the OpenSwoole response object
 */
function get_openswoole_response() {
    return $GLOBALS['swoole_response'] ?? null;
}

/**
 * Helper function to safely end the response
 */
function openswoole_end_response(string $content = ''): void {
    $response = get_openswoole_response();
    if ($response && $response->isWritable()) {
        $response->end($content);
    }
}

/**
 * Helper function to safely send headers
 */
function openswoole_send_header(string $name, string $value): void {
    $response = get_openswoole_response();
    if ($response && $response->isWritable()) {
        $response->header($name, $value);
    }
}

/**
 * Helper function to safely set status code
 */
function openswoole_set_status(int $code): void {
    $response = get_openswoole_response();
    if ($response && $response->isWritable()) {
        $response->status($code);
    }
}
