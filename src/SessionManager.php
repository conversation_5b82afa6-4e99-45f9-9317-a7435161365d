<?php

namespace MikhMon;

use OpenSwoole\HTTP\Request;
use OpenSwoole\HTTP\Response;

class SessionManager
{
    private array $sessions = [];
    private string $sessionName = 'MIKHMON_SESSID';
    private int $sessionLifetime = 3600; // 1 hour
    private string $sessionPath;

    public function __construct()
    {
        $this->sessionPath = __DIR__ . '/../sessions';
        if (!is_dir($this->sessionPath)) {
            mkdir($this->sessionPath, 0755, true);
        }
    }

    public function initSession(Request $request, Response $response): void
    {
        $sessionId = $this->getSessionId($request);
        
        if (!$sessionId || !$this->isValidSession($sessionId)) {
            $sessionId = $this->generateSessionId();
            $this->setSessionCookie($response, $sessionId);
        }
        
        $this->loadSession($sessionId);
        $this->setCurrentSessionId($sessionId);
    }

    private function getSessionId(Request $request): ?string
    {
        return $request->cookie[$this->sessionName] ?? null;
    }

    private function generateSessionId(): string
    {
        return bin2hex(random_bytes(16));
    }

    private function setSessionCookie(Response $response, string $sessionId): void
    {
        $response->cookie(
            $this->sessionName,
            $sessionId,
            time() + $this->sessionLifetime,
            '/',
            '',
            false,
            true
        );
    }

    private function isValidSession(string $sessionId): bool
    {
        $sessionFile = $this->getSessionFile($sessionId);
        
        if (!file_exists($sessionFile)) {
            return false;
        }
        
        $lastModified = filemtime($sessionFile);
        return (time() - $lastModified) < $this->sessionLifetime;
    }

    private function loadSession(string $sessionId): void
    {
        $sessionFile = $this->getSessionFile($sessionId);
        
        if (file_exists($sessionFile)) {
            $data = file_get_contents($sessionFile);
            $this->sessions[$sessionId] = $data ? unserialize($data) : [];
        } else {
            $this->sessions[$sessionId] = [];
        }
    }

    private function getSessionFile(string $sessionId): string
    {
        return $this->sessionPath . '/sess_' . $sessionId;
    }

    private function setCurrentSessionId(string $sessionId): void
    {
        // Store current session ID for global access
        $GLOBALS['current_session_id'] = $sessionId;
    }

    public function getCurrentSessionId(): ?string
    {
        return $GLOBALS['current_session_id'] ?? null;
    }

    public function get(string $key, $default = null)
    {
        $sessionId = $this->getCurrentSessionId();
        if (!$sessionId) {
            return $default;
        }
        
        return $this->sessions[$sessionId][$key] ?? $default;
    }

    public function set(string $key, $value): void
    {
        $sessionId = $this->getCurrentSessionId();
        if (!$sessionId) {
            return;
        }
        
        $this->sessions[$sessionId][$key] = $value;
        $this->saveSession($sessionId);
    }

    public function has(string $key): bool
    {
        $sessionId = $this->getCurrentSessionId();
        if (!$sessionId) {
            return false;
        }
        
        return isset($this->sessions[$sessionId][$key]);
    }

    public function remove(string $key): void
    {
        $sessionId = $this->getCurrentSessionId();
        if (!$sessionId) {
            return;
        }
        
        unset($this->sessions[$sessionId][$key]);
        $this->saveSession($sessionId);
    }

    public function destroy(): void
    {
        $sessionId = $this->getCurrentSessionId();
        if (!$sessionId) {
            return;
        }
        
        unset($this->sessions[$sessionId]);
        $sessionFile = $this->getSessionFile($sessionId);
        if (file_exists($sessionFile)) {
            unlink($sessionFile);
        }
    }

    public function getAll(): array
    {
        $sessionId = $this->getCurrentSessionId();
        if (!$sessionId) {
            return [];
        }
        
        return $this->sessions[$sessionId] ?? [];
    }

    private function saveSession(string $sessionId): void
    {
        $sessionFile = $this->getSessionFile($sessionId);
        $data = serialize($this->sessions[$sessionId] ?? []);
        file_put_contents($sessionFile, $data, LOCK_EX);
    }

    public function cleanupExpiredSessions(): void
    {
        $files = glob($this->sessionPath . '/sess_*');
        $now = time();
        
        foreach ($files as $file) {
            if (($now - filemtime($file)) > $this->sessionLifetime) {
                unlink($file);
            }
        }
    }
}
