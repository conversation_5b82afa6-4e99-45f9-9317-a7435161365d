<?php

namespace MikhMon;

/**
 * Pre-Include Handler for OpenSwoole
 * 
 * This class handles modifications to PHP files before they are included
 * to make them compatible with OpenSwoole.
 */
class PreIncludeHandler
{
    private static array $processedFiles = [];

    public static function processFile(string $filePath): string
    {
        // Check if file has already been processed
        $fileKey = md5($filePath);
        if (isset(self::$processedFiles[$fileKey])) {
            return self::$processedFiles[$fileKey];
        }

        if (!file_exists($filePath)) {
            return $filePath;
        }

        $content = file_get_contents($filePath);
        $originalContent = $content;

        // Replace problematic ob_start calls
        $content = self::replaceObStart($content);
        
        // Replace problematic header calls
        $content = self::replaceHeaderCalls($content);
        
        // Replace exit/die calls in certain contexts
        $content = self::replaceExitCalls($content);

        // If content was modified, create a temporary file
        if ($content !== $originalContent) {
            $tempFile = self::createTempFile($content, $filePath);
            self::$processedFiles[$fileKey] = $tempFile;
            return $tempFile;
        }

        self::$processedFiles[$fileKey] = $filePath;
        return $filePath;
    }

    private static function replaceObStart(string $content): string
    {
        // Replace ob_start("ob_gzhandler") with a compatible version
        $patterns = [
            '/ob_start\s*\(\s*["\']ob_gzhandler["\']\s*\)/' => 'ob_start(function($buffer) { return $buffer; })',
            '/ob_start\s*\(\s*"ob_gzhandler"\s*\)/' => 'ob_start(function($buffer) { return $buffer; })',
            '/ob_start\s*\(\s*\'ob_gzhandler\'\s*\)/' => 'ob_start(function($buffer) { return $buffer; })',
        ];

        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }

        return $content;
    }

    private static function replaceHeaderCalls(string $content): string
    {
        // Replace header() calls with safe versions in certain contexts
        // This is more complex and might not be needed for basic functionality
        return $content;
    }

    private static function replaceExitCalls(string $content): string
    {
        // Replace exit() calls in JavaScript redirects with safer alternatives
        // This is more complex and might not be needed for basic functionality
        return $content;
    }

    private static function createTempFile(string $content, string $originalPath): string
    {
        $tempDir = sys_get_temp_dir() . '/mikhmon_openswoole';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $fileName = basename($originalPath);
        $tempFile = $tempDir . '/' . md5($originalPath) . '_' . $fileName;
        
        file_put_contents($tempFile, $content);
        
        // Register for cleanup
        register_shutdown_function(function() use ($tempFile) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        });

        return $tempFile;
    }

    public static function cleanup(): void
    {
        $tempDir = sys_get_temp_dir() . '/mikhmon_openswoole';
        if (is_dir($tempDir)) {
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            rmdir($tempDir);
        }
        self::$processedFiles = [];
    }
}
