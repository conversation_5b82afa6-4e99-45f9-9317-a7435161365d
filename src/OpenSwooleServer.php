<?php

namespace MikhMon;

use OpenSwoole\HTTP\Server;
use OpenSwoole\HTTP\Request;
use OpenSwoole\HTTP\Response;

class OpenSwooleServer
{
    private Server $server;
    private array $config;
    private SessionManager $sessionManager;
    private Router $router;

    public function __construct(array $config)
    {
        $this->config = $this->validateConfig($config);
        $this->server = new Server($this->config['host'], $this->config['port']);
        $this->sessionManager = new SessionManager();
        $this->router = new Router();

        $this->configure();
        $this->setupHandlers();
    }

    private function validateConfig(array $config): array
    {
        $defaults = [
            'host' => '0.0.0.0',
            'port' => 8080,
            'document_root' => dirname(__DIR__),
            'enable_static_handler' => true,
            'static_handler_locations' => ['/css', '/js', '/img', '/lang'],
            'worker_num' => 4,
            'task_worker_num' => 0, // Set to 0 to disable task workers
            'max_request' => 1000,
            'dispatch_mode' => 2,
            'daemonize' => false,
            'log_file' => dirname(__DIR__) . '/logs/swoole.log',
            'log_level' => SWOOLE_LOG_INFO,
            'pid_file' => dirname(__DIR__) . '/logs/swoole.pid',
        ];

        $merged = array_merge($defaults, $config);

        // Ensure log directory exists
        $logDir = dirname($merged['log_file']);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Ensure pid directory exists
        $pidDir = dirname($merged['pid_file']);
        if (!is_dir($pidDir)) {
            mkdir($pidDir, 0755, true);
        }

        return $merged;
    }

    private function configure(): void
    {
        $this->server->set([
            'document_root' => $this->config['document_root'],
            'enable_static_handler' => $this->config['enable_static_handler'],
            'static_handler_locations' => $this->config['static_handler_locations'],
            'worker_num' => $this->config['worker_num'],
            'task_worker_num' => $this->config['task_worker_num'],
            'max_request' => $this->config['max_request'],
            'dispatch_mode' => $this->config['dispatch_mode'],
            'daemonize' => $this->config['daemonize'],
            'log_file' => $this->config['log_file'],
            'log_level' => $this->config['log_level'],
            'pid_file' => $this->config['pid_file'],
        ]);
    }

    private function setupHandlers(): void
    {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('request', [$this, 'onRequest']);

        // Only add task handlers if task workers are enabled
        if ($this->config['task_worker_num'] > 0) {
            $this->server->on('task', [$this, 'onTask']);
            $this->server->on('finish', [$this, 'onFinish']);
        }
    }

    public function onStart(Server $server): void
    {
        echo "OpenSwoole HTTP Server started at http://{$this->config['host']}:{$this->config['port']}\n";
        echo "Master PID: {$server->master_pid}\n";
        echo "Manager PID: {$server->manager_pid}\n";

        // Create logs directory if it doesn't exist
        $logsDir = dirname($this->config['log_file']);
        if (!empty($logsDir) && !is_dir($logsDir)) {
            if (!mkdir($logsDir, 0755, true) && !is_dir($logsDir)) {
                echo "Warning: Could not create logs directory: $logsDir\n";
            }
        }
    }

    public function onWorkerStart(Server $server, int $workerId): void
    {
        echo "Worker #{$workerId} started\n";
    }

    public function onRequest(Request $request, Response $response): void
    {
        try {
            // Initialize session for this request
            $this->sessionManager->initSession($request, $response);
            
            // Handle the request through router
            $this->router->handle($request, $response, $this->sessionManager);
            
        } catch (\Throwable $e) {
            $this->handleError($response, $e);
        }
    }

    private function handleError(Response $response, \Throwable $e): void
    {
        $response->status(500);
        $response->header('Content-Type', 'text/html; charset=utf-8');
        
        $errorHtml = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Server Error</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; }
                .trace { background: #f8f9fa; padding: 15px; margin-top: 20px; border-radius: 5px; overflow-x: auto; }
                pre { margin: 0; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='error'>
                <h2>Server Error</h2>
                <p><strong>Message:</strong> {$e->getMessage()}</p>
                <p><strong>File:</strong> {$e->getFile()}:{$e->getLine()}</p>
            </div>
            <div class='trace'>
                <h3>Stack Trace:</h3>
                <pre>{$e->getTraceAsString()}</pre>
            </div>
        </body>
        </html>";
        
        $response->end($errorHtml);
    }

    public function onTask(Server $server, int $taskId, int $reactorId, $data)
    {
        // Handle background tasks if needed
        // For now, just return the data
        return $data;
    }

    public function onFinish(Server $server, int $taskId, $data): void
    {
        // Handle task completion if needed
        echo "Task #{$taskId} finished\n";
    }

    public function start(): void
    {
        $this->server->start();
    }
}
