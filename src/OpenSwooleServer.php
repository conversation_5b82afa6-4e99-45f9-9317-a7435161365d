<?php

namespace MikhMon;

use OpenSwoole\HTTP\Server;
use OpenSwoole\HTTP\Request;
use OpenSwoole\HTTP\Response;

class OpenSwooleServer
{
    private Server $server;
    private array $config;
    private SessionManager $sessionManager;
    private Router $router;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->server = new Server($config['host'], $config['port']);
        $this->sessionManager = new SessionManager();
        $this->router = new Router();
        
        $this->configure();
        $this->setupHandlers();
    }

    private function configure(): void
    {
        $this->server->set([
            'document_root' => $this->config['document_root'],
            'enable_static_handler' => $this->config['enable_static_handler'],
            'static_handler_locations' => $this->config['static_handler_locations'],
            'worker_num' => $this->config['worker_num'],
            'task_worker_num' => $this->config['task_worker_num'],
            'max_request' => $this->config['max_request'],
            'dispatch_mode' => $this->config['dispatch_mode'],
            'daemonize' => $this->config['daemonize'],
            'log_file' => $this->config['log_file'],
            'log_level' => $this->config['log_level'],
            'pid_file' => $this->config['pid_file'],
        ]);
    }

    private function setupHandlers(): void
    {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('request', [$this, 'onRequest']);
    }

    public function onStart(Server $server): void
    {
        echo "OpenSwoole HTTP Server started at http://{$this->config['host']}:{$this->config['port']}\n";
        echo "Master PID: {$server->master_pid}\n";
        echo "Manager PID: {$server->manager_pid}\n";
        
        // Create logs directory if it doesn't exist
        $logsDir = dirname($this->config['log_file']);
        if (!is_dir($logsDir)) {
            mkdir($logsDir, 0755, true);
        }
    }

    public function onWorkerStart(Server $server, int $workerId): void
    {
        echo "Worker #{$workerId} started\n";
    }

    public function onRequest(Request $request, Response $response): void
    {
        try {
            // Initialize session for this request
            $this->sessionManager->initSession($request, $response);
            
            // Handle the request through router
            $this->router->handle($request, $response, $this->sessionManager);
            
        } catch (\Throwable $e) {
            $this->handleError($response, $e);
        }
    }

    private function handleError(Response $response, \Throwable $e): void
    {
        $response->status(500);
        $response->header('Content-Type', 'text/html; charset=utf-8');
        
        $errorHtml = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Server Error</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; }
                .trace { background: #f8f9fa; padding: 15px; margin-top: 20px; border-radius: 5px; overflow-x: auto; }
                pre { margin: 0; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='error'>
                <h2>Server Error</h2>
                <p><strong>Message:</strong> {$e->getMessage()}</p>
                <p><strong>File:</strong> {$e->getFile()}:{$e->getLine()}</p>
            </div>
            <div class='trace'>
                <h3>Stack Trace:</h3>
                <pre>{$e->getTraceAsString()}</pre>
            </div>
        </body>
        </html>";
        
        $response->end($errorHtml);
    }

    public function start(): void
    {
        $this->server->start();
    }
}
