# MikhMon V3 with OpenSwoole

This document explains how to run MikhMon V3 using OpenSwoole instead of the traditional PHP-FPM + Nginx setup.

## What is OpenSwoole?

OpenSwoole is a high-performance, scalable, concurrent networking framework for PHP. It provides:

- **Better Performance**: Significantly faster than traditional PHP-FPM setups
- **Built-in HTTP Server**: No need for separate web server like Nginx/Apache
- **Persistent Connections**: Maintains database and API connections across requests
- **Coroutines**: Asynchronous programming support
- **WebSocket Support**: Real-time communication capabilities

## Requirements

- PHP 8.0 or higher
- OpenSwoole extension
- Composer

## Installation Methods

### Method 1: Using Docker (Recommended)

1. **Build and run with OpenSwoole:**
   ```bash
   # Start with OpenSwoole
   docker-compose --profile openswoole up -d
   
   # Or build manually
   docker build -f Dockerfile.openswoole -t mikhmon-openswoole .
   docker run -p 8080:8080 mikhmon-openswoole
   ```

2. **Access the application:**
   - Open http://localhost:8080
   - Default credentials: username `mikh<PERSON>`, password `1234`

### Method 2: Manual Installation

1. **Install OpenSwoole extension:**
   ```bash
   # Using PECL
   pecl install openswoole
   
   # Add to php.ini
   echo "extension=openswoole" >> /etc/php/php.ini
   ```

2. **Install Composer dependencies:**
   ```bash
   composer install --no-dev --optimize-autoloader
   ```

3. **Start the server:**
   ```bash
   # Using the startup script
   ./start-openswoole.sh
   
   # Or directly
   php server.php
   ```

4. **Access the application:**
   - Open http://localhost:8080

## Configuration

### Server Configuration

Edit `server.php` to modify server settings:

```php
$config = [
    'host' => '0.0.0.0',           // Server host
    'port' => 8080,                // Server port
    'worker_num' => 4,             // Number of worker processes
    'task_worker_num' => 2,        // Number of task worker processes
    'max_request' => 1000,         // Max requests per worker
    'daemonize' => false,          // Run as daemon
    'log_file' => __DIR__ . '/logs/swoole.log',
    'log_level' => SWOOLE_LOG_INFO,
];
```

### Session Configuration

Sessions are handled by the `SessionManager` class with file-based storage:

- Session files are stored in the `sessions/` directory
- Session lifetime: 1 hour (configurable)
- Automatic cleanup of expired sessions

## Architecture

### Components

1. **OpenSwooleServer**: Main server class that handles HTTP requests
2. **Router**: Routes requests to appropriate PHP files, maintaining compatibility
3. **SessionManager**: Handles session management for stateless OpenSwoole
4. **SessionCompatibility**: Provides compatibility layer for PHP session functions

### Request Flow

```
Client Request → OpenSwoole Server → Router → Legacy PHP Files → Response
                      ↓
                 SessionManager (handles sessions)
                      ↓
              SessionCompatibility (PHP session functions)
```

## Comparison: Traditional vs OpenSwoole

| Feature | Traditional (PHP-FPM + Nginx) | OpenSwoole |
|---------|-------------------------------|------------|
| Performance | Moderate | High |
| Memory Usage | Higher (separate processes) | Lower (shared memory) |
| Startup Time | Slower | Faster |
| Persistent Connections | No | Yes |
| WebSocket Support | Requires additional setup | Built-in |
| Configuration Complexity | Higher | Lower |

## Performance Benefits

- **Faster Response Times**: 2-5x faster than traditional setups
- **Lower Memory Usage**: Shared memory across workers
- **Persistent Database Connections**: No connection overhead per request
- **Built-in Load Balancing**: Automatic request distribution across workers

## Switching Between Setups

### Start Traditional Setup:
```bash
docker-compose --profile traditional up -d
```

### Start OpenSwoole Setup:
```bash
docker-compose --profile openswoole up -d
```

### Stop All Services:
```bash
docker-compose down
```

## Troubleshooting

### Common Issues

1. **OpenSwoole extension not found:**
   ```bash
   # Check if extension is loaded
   php -m | grep openswoole
   
   # Install if missing
   pecl install openswoole
   ```

2. **Permission denied for logs/sessions directories:**
   ```bash
   mkdir -p logs sessions
   chmod 755 logs sessions
   ```

3. **Port already in use:**
   - Change the port in `server.php` or `docker-compose.yml`
   - Or stop the conflicting service

### Logs

- **Server logs**: `logs/swoole.log`
- **Error logs**: Check console output when running `php server.php`

## Development

### Adding New Features

1. Modify the appropriate PHP files (existing MikhMon structure)
2. No changes needed to OpenSwoole setup for most features
3. For WebSocket features, extend the `OpenSwooleServer` class

### Debugging

1. **Enable debug mode:**
   ```php
   // In server.php
   $config['log_level'] = SWOOLE_LOG_DEBUG;
   ```

2. **Use var_dump/error_log** in PHP files as usual

## Migration Notes

- **Session Handling**: Automatically handled by compatibility layer
- **File Uploads**: Supported through OpenSwoole's request handling
- **Database Connections**: Can be made persistent for better performance
- **Existing Code**: No changes required to existing MikhMon PHP files

## Production Deployment

1. **Set daemon mode:**
   ```php
   $config['daemonize'] = true;
   ```

2. **Configure proper logging:**
   ```php
   $config['log_file'] = '/var/log/mikhmon/swoole.log';
   ```

3. **Use process manager** (systemd, supervisor, etc.)

4. **Set up reverse proxy** if needed (for SSL termination)

## Support

For issues specific to the OpenSwoole implementation, check:
- Server logs in `logs/swoole.log`
- PHP error logs
- OpenSwoole documentation: https://openswoole.com/

For MikhMon-specific issues, refer to the main README.md file.
