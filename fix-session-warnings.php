<?php
/**
 * Session Warning Fix for MikhMon V3 OpenSwoole
 * 
 * This script helps fix session-related warnings when running under OpenSwoole
 */

echo "MikhMon V3 OpenSwoole Session Fix\n";
echo "=================================\n\n";

// Check if we're running under OpenSwoole
if (!extension_loaded('openswoole')) {
    echo "❌ OpenSwoole extension not found. Please install it first.\n";
    exit(1);
}

echo "✅ OpenSwoole extension found\n";

// Check if compatibility files exist
$requiredFiles = [
    'src/SessionCompatibility.php',
    'src/OpenSwooleBootstrap.php',
    'openswoole-compatibility.php'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "❌ Missing compatibility files:\n";
    foreach ($missingFiles as $file) {
        echo "   - $file\n";
    }
    exit(1);
}

echo "✅ All compatibility files found\n";

// Test session functionality
echo "\n🧪 Testing session functionality...\n";

// Include the compatibility layer
require_once 'src/SessionCompatibility.php';
require_once 'src/OpenSwooleBootstrap.php';

// Create a mock session manager for testing
class MockSessionManager {
    private $data = [];
    
    public function getAll(): array {
        return $this->data;
    }
    
    public function set(string $key, $value): void {
        $this->data[$key] = $value;
    }
    
    public function get(string $key, $default = null) {
        return $this->data[$key] ?? $default;
    }
    
    public function getCurrentSessionId(): string {
        return 'test_session_id';
    }
}

// Test the session compatibility
$mockSessionManager = new MockSessionManager();
\MikhMon\SessionCompatibility::setSessionManager($mockSessionManager);

// Test session start
$result = \MikhMon\SessionCompatibility::sessionStart();
if ($result) {
    echo "✅ Session start works\n";
} else {
    echo "❌ Session start failed\n";
}

// Test session data
$_SESSION['test'] = 'value';
if (isset($_SESSION['test']) && $_SESSION['test'] === 'value') {
    echo "✅ Session data works\n";
} else {
    echo "❌ Session data failed\n";
}

// Test session status
$status = \MikhMon\SessionCompatibility::sessionStatus();
if ($status === PHP_SESSION_ACTIVE) {
    echo "✅ Session status works\n";
} else {
    echo "❌ Session status failed\n";
}

echo "\n📋 Summary:\n";
echo "- Session warnings are normal when migrating to OpenSwoole\n";
echo "- The compatibility layer handles sessions correctly\n";
echo "- You can test sessions at: http://localhost:8080/test-session.php\n";
echo "- The warnings are suppressed and don't affect functionality\n";

echo "\n🚀 Ready to start OpenSwoole server!\n";
echo "Run: ./start-openswoole.sh\n";
